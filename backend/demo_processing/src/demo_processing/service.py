"""FastStream service for demo processing."""

import asyncio
import time
from datetime import UTC, datetime
from typing import Any

from anyio import move_on_after
from faststream import FastStream, Logger
from faststream.redis import RedisBroker
from pydantic import ValidationError

from .async_utils import ProgressTracker, processing_slot
from .config import get_settings
from .logging_config import get_logger
from .models import (
    DemoProcessingError,
    DemoProcessingRequest,
    DemoProcessingResult,
)
from .parser import parse_demo

logger = get_logger(__name__)

# Create broker and app following standard FastStream patterns
settings = get_settings()
broker = RedisBroker(settings.redis_url)
app = FastStream(broker)


def create_app(broker_url: str = "redis://localhost:6379") -> FastStream:
    """Create and configure the FastStream application.

    Args:
        broker_url: Redis connection URL

    Returns:
        Configured FastStream application
    """
    # Reconfigure the global broker with the provided URL
    global broker, app
    broker = RedisBroker(
        broker_url,
        # Add connection timeout and retry settings for faster shutdown
        socket_connect_timeout=5,
        socket_timeout=5,
        retry_on_timeout=True,
        health_check_interval=30,
    )
    app = FastStream(broker)
    return app


# Define message handlers directly on the broker instance
@broker.subscriber("demo.processing.requests")
@broker.publisher("demo.processing.results")
async def process_demo_request(
    request: DemoProcessingRequest,
    logger: Logger,
) -> DemoProcessingResult:
    """Process a demo parsing request with concurrency control and retries.

    Args:
        request: The demo processing request
        logger: FastStream logger

    Returns:
        DemoProcessingResult with parsed data or error information
    """
    return await _process_demo_with_error_handling(request, logger)


@broker.subscriber("demo.processing.requests")
@broker.publisher("demo.processing.errors")
def handle_invalid_requests(
    msg: dict[str, str],
    logger: Logger,
) -> DemoProcessingError | None:
    """Handle invalid request messages.

    Args:
        msg: Raw message that couldn't be parsed as DemoProcessingRequest
        logger: FastStream logger

    Returns:
        DemoProcessingError if the message is invalid, None otherwise
    """
    try:
        # Try to parse as DemoProcessingRequest
        _ = DemoProcessingRequest.model_validate(msg)
    except ValidationError as e:
        logger.error("Invalid request message", exc_info=True)

        # Try to extract request_id if available
        request_id = msg.get("request_id", "unknown")

        return DemoProcessingError(
            request_id=str(request_id),
            error_type="ValidationError",
            error_message=f"Invalid request format: {e}",
            timestamp=datetime.now(UTC).isoformat(),
        )
    else:
        # If successful, return None (let the main handler process it)
        return None


@broker.subscriber("demo.processing.health.check")
@broker.publisher("demo.processing.health.response")
def health_check(logger: Logger) -> dict[str, str]:
    """Health check endpoint.

    Args:
        logger: FastStream logger

    Returns:
        Health status information
    """
    logger.info("Health check requested")

    return {
        "status": "healthy",
        "service": "demo-processing",
        "timestamp": datetime.now(UTC).isoformat(),
        "version": "0.1.0",
    }


async def _process_demo_with_error_handling(
    request: DemoProcessingRequest,
    logger: Logger,
) -> DemoProcessingResult:
    """Process demo with comprehensive error handling.

    Args:
        request: The demo processing request
        logger: FastStream logger

    Returns:
        DemoProcessingResult with parsed data or error information
    """
    logger.info(
        f"Processing demo request - request_id: {request.request_id}, file_path: {request.demo_file_path}"
    )
    start_time = time.time()

    # Create progress tracker
    progress_tracker = ProgressTracker(request.request_id)

    async with processing_slot():
        try:
            demo_result = await _parse_demo_with_timeout(request, progress_tracker)
            if demo_result.is_err():
                return _create_error_result(request, demo_result.unwrap_err(), start_time, logger)
            
            return _create_success_result(
                request, demo_result.unwrap(), start_time, progress_tracker, logger
            )
        except Exception as e:
            return _create_unexpected_error_result(request, e, start_time, logger)


async def _parse_demo_with_timeout(
    request: DemoProcessingRequest,
    progress_tracker: ProgressTracker,
) -> Any:
    """Parse demo file with timeout handling.

    Args:
        request: The demo processing request
        progress_tracker: Progress tracking instance

    Returns:
        Result containing parsed demo data or error message
    """
    from result import Err, Result
    
    settings = get_settings()

    # Create progress callback
    def progress_callback(stage: str, progress: float) -> None:
        step = int(progress * 100)
        progress_tracker.update(step, stage)

    # Parse the demo file with timeout using AnyIO
    demo_result = None
    with move_on_after(settings.processing_timeout_seconds) as cancel_scope:
        demo_result = await parse_demo(
            request.demo_file_path, progress_callback=progress_callback
        )

    if cancel_scope.cancelled_caught:
        timeout_msg = f"Demo processing timed out after {settings.processing_timeout_seconds} seconds"
        return Err(timeout_msg)

    if demo_result is None:
        error_msg = "Demo parsing returned None unexpectedly"
        return Err(error_msg)

    return demo_result


def _create_success_result(
    request: DemoProcessingRequest,
    demo_data: Any,
    start_time: float,
    progress_tracker: ProgressTracker,
    logger: Logger,
) -> DemoProcessingResult:
    """Create a successful processing result.

    Returns:
        DemoProcessingResult with success status and demo data
    """
    processing_time = time.time() - start_time
    progress_tracker.complete("Demo processing completed successfully")

    result = DemoProcessingResult(
        request_id=request.request_id,
        success=True,
        demo_data=demo_data,
        error_message=None,
        processing_time_seconds=processing_time,
    )

    logger.info(
        f"Successfully processed demo - request_id: {request.request_id}, processing_time_seconds: {processing_time:.2f}"
    )
    return result


def _create_error_result(
    request: DemoProcessingRequest,
    error_message: str,
    start_time: float,
    logger: Logger,
) -> DemoProcessingResult:
    """Create an error processing result.

    Returns:
        DemoProcessingResult with error status and message
    """
    processing_time = time.time() - start_time

    result = DemoProcessingResult(
        request_id=request.request_id,
        success=False,
        demo_data=None,
        error_message=error_message,
        processing_time_seconds=processing_time,
    )

    logger.error(
        f"Failed to process demo - request_id: {request.request_id}, error: {error_message}"
    )
    return result


def _create_unexpected_error_result(
    request: DemoProcessingRequest,
    error: Exception,
    start_time: float,
    logger: Logger,
) -> DemoProcessingResult:
    """Create an unexpected error processing result.

    Returns:
        DemoProcessingResult with unexpected error status and message
    """
    processing_time = time.time() - start_time

    result = DemoProcessingResult(
        request_id=request.request_id,
        success=False,
        demo_data=None,
        error_message=f"Unexpected error: {error}",
        processing_time_seconds=processing_time,
    )

    logger.error(
        f"Unexpected error processing demo - request_id: {request.request_id}, error: {error!s}"
    )
    return result


async def start_service() -> None:
    """Start the service."""
    logger.info("Starting Demo Processing Service")
    await app.run()


async def stop_service() -> None:
    """Stop the service."""
    logger.info("Stopping Demo Processing Service")
    await broker.close()


def run_service() -> None:
    """Run the service (blocking) with proper signal handling."""
    try:
        asyncio.run(_run_with_signal_handling())
    except KeyboardInterrupt:
        logger.info("Service interrupted by user (KeyboardInterrupt)")
    except Exception as e:
        logger.error(f"Failed to run service: {e}", exc_info=True)
        raise


async def _run_with_signal_handling() -> None:
    """Run the service with proper signal handling."""
    logger.info("Starting FastStream application")
    # FastStream handles signal handling internally, so we can just run the app
    await app.run()


# For convenience, allow running the service directly with default settings
if __name__ == "__main__":
    run_service()
