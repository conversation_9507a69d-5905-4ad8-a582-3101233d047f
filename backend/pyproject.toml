[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi[standard]>=0.115.11",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.8.1",
    "sqlalchemy>=2.0.0",
    "httpx>=0.27.0",
    "faststream[redis]>=0.5.42",
    "demoparser2>=0.38.0",
    "awpy>=2.0.2",
    "structlog>=24.5.0",
    "aiofiles>=24.1.0",
    "anyio>=4.8.0",
    "typer[all]>=0.15.1",
    "rich>=13.9.4",
    "core",
    "result>=0.17.0",
    "pandas-stubs>=2.2.3.250527",
]

[dependency-groups]
dev = [
    "basedpyright>=1.29.1",
    "pytest>=8.3.5",
    "ruff>=0.9.9",
]
testing = [
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
    "testcontainers>=4.8.3",
    "psycopg2-binary>=2.9.10",
    "sqlmodel>=0.0.24",
    "pytest-cov>=6.1.1",
]

[tool.basedpyright]
reportUnusedCallResult = false
include = ["src/**"]

[tool.ruff]
target-version = "py313"

[tool.ruff.lint]
extend-select = ["D", "DOC", "UP", "FURB", "RUF", "TRY", "F", "E", "F", "PERF",
                 "N", "NPY", "C90", "I", "PTH", "TC", "TD", "SIM", "RET", "Q",
                 "PT", "PYI", "T20", "PIE", "LOG", "ISC", "FA", "FIX", "EM", "DTZ",
                 "C4", "A", "B", "BLE", "S", "ASYNC", "ANN", "YTT", "FAST", "ERA", "B",
                 "TC"]
preview = true
ignore = ["D100", "E501", "ANN401", "D105", "S101", "S311", "S403", "ISC003", "ASYNC109", "S301"]
extend-fixable = ["D301", "D205"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pydoclint]
ignore-one-line-docstrings = true

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
ignore-fully-untyped = true
mypy-init-return = true
suppress-none-returning = true

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = true

[tool.ruff.lint.per-file-ignores]
"**/tests/*" = ["DOC", "D"]
"**/tests/__init__.py" = ["D104"]

[project.scripts]
brainless-cli = "backend.cli:app"

[tool.uv.workspace]
members = [
    "demo_processing",
    "packages/core",
]

[tool.uv.sources]
core = { workspace = true }

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
