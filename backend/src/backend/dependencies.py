"""FastAPI dependencies for services and clients."""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends

from backend import demo_storage
from backend.clients.demo_processing_client import DemoProcessingClient
from backend.clients.steam_api import SteamAPI, get_steam_api
from backend.config import settings
from backend.services.demo_service import DemoService
from backend.services.match_service import MatchService
from backend.services.user_service import UserService


# Global demo processing client instance with proper lifecycle management
_demo_processing_client: DemoProcessingClient | None = None


async def get_demo_processing_client() -> DemoProcessingClient:
    """Get demo processing client instance (singleton with proper lifecycle).

    Returns:
        DemoProcessingClient instance
    """
    global _demo_processing_client
    if _demo_processing_client is None:
        _demo_processing_client = DemoProcessingClient(broker_url=settings.REDIS_URL)
        # Ensure the client is started
        await _demo_processing_client._ensure_broker_started()
    return _demo_processing_client


async def cleanup_demo_processing_client():
    """Cleanup the demo processing client."""
    global _demo_processing_client
    if _demo_processing_client is not None:
        await _demo_processing_client.close()
        _demo_processing_client = None


@lru_cache()
def get_demo_storage() -> demo_storage.DemoStorage:
    """Get demo storage instance (cached).

    Returns:
        DemoStorage instance
    """
    return demo_storage.get_demo_storage()


def get_user_service(
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)]
) -> UserService:
    """Get user service instance.

    Args:
        steam_api: Steam API client

    Returns:
        UserService instance
    """
    return UserService(steam_api)


def get_match_service(
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)]
) -> MatchService:
    """Get match service instance.

    Args:
        steam_api: Steam API client

    Returns:
        MatchService instance
    """
    return MatchService(steam_api)


def get_demo_service(
    demo_processing_client: Annotated[DemoProcessingClient, Depends(get_demo_processing_client)],
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
    demo_storage_instance: Annotated[demo_storage.DemoStorage, Depends(get_demo_storage)],
) -> DemoService:
    """Get demo service instance.

    Args:
        demo_processing_client: Demo processing microservice client
        steam_api: Steam API client
        demo_storage_instance: Demo storage handler

    Returns:
        DemoService instance
    """
    return DemoService(demo_processing_client, steam_api, demo_storage_instance)
