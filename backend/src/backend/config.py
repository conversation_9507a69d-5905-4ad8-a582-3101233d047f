import secrets
from functools import lru_cache
from pathlib import Path
from typing import ClassV<PERSON>, Literal

from pydantic import <PERSON><PERSON>Dsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Settings for the backend."""

    ENV: Literal["development", "staging", "production"] = "development"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    DATABASE_URI: str = "sqlite:///./database.db"
    STEAM_API_KEY: str = ""  # Must be set in .env file
    # Demo storage settings
    DEMO_STORAGE_PATH: Path = Path("./storage/demos").absolute()
    DEMO_RETENTION_DAYS: int = 7  # Number of days to keep processed demos
    # HTTP client settings
    REQUEST_TIMEOUT: int = 10  # Timeout in seconds for API requests
    # Redis/Demo processing settings
    REDIS_URL: RedisDsn = RedisDsn("redis://localhost:6379")

    model_config: ClassVar[SettingsConfigDict] = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
    )

    @field_validator("DEMO_STORAGE_PATH")
    @classmethod
    def ensure_data_dirs_exist(cls, v: Path) -> None:
        """Ensure data directories exist."""
        v.mkdir(parents=True, exist_ok=True)


@lru_cache
def get_settings() -> Settings:
    """Get application settings.

    Returns:
        Settings instance
    """
    return Settings()


# Keep the old settings instance for backward compatibility during transition
settings = get_settings()
