"""Demo file management service."""

from typing import Any

import httpx
from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile, status

from backend import crud, demo_storage, schemas
from backend.clients.demo_processing_client import DemoProcessingClient
from backend.clients.steam_api import SteamAPI
from backend.database import get_connection
from backend.logging_config import get_logger

logger = get_logger(__name__)


class DemoService:
    """Service for managing demo files and processing."""

    def __init__(
        self,
        demo_processing_client: DemoProcessingClient,
        steam_api: SteamAPI,
        demo_storage_instance: demo_storage.DemoStorage,
    ):
        """Initialize the demo service.

        Args:
            demo_processing_client: Client for demo processing microservice
            steam_api: Steam API client
            demo_storage_instance: Demo storage handler
        """
        self.demo_processing_client = demo_processing_client
        self.steam_api = steam_api
        self.demo_storage = demo_storage_instance

    async def request_demo_download(self, match_id: str) -> schemas.DemoFileRead:
        """Request a demo file download for a match.

        Args:
            match_id: The match ID to download the demo for

        Returns:
            The created demo file record

        Raises:
            HTTPException: If demo already exists or Steam API fails
        """
        with get_connection() as conn:
            # Check if we already have this demo
            try:
                return crud.read_demo_file_by_match(match_id, conn)
            except HTTPException:
                # Demo not found, continue with download
                pass

            # Get match info from Steam
            match_info = await self.steam_api.get_match_info(match_id)

            # Create the demo file record
            file_path = self.demo_storage.generate_file_path(match_id)
            demo = schemas.DemoFileCreate(
                match_id=match_id,
                file_path=str(file_path),
            )
            demo_file = crud.create_demo_file(demo, conn)

            # Start background download
            await self._download_demo_async(match_id, match_info.demo_url)

            return demo_file

    async def upload_demo_file(
        self, match_id: str, demo_file: UploadFile
    ) -> schemas.DemoFileRead:
        """Upload a demo file manually.

        Args:
            match_id: The match ID to associate with the demo file
            demo_file: The uploaded demo file

        Returns:
            The created demo file record

        Raises:
            HTTPException: If the file is not valid or there's an error saving it
        """
        # Validate file
        if not demo_file.filename or not demo_file.filename.endswith(".dem"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be a .dem file",
            )

        # Read file content
        content = await demo_file.read()

        # Save the demo file
        file_path = await self.demo_storage.save_demo_file(match_id, content)

        # Create the demo file record
        with get_connection() as conn:
            demo = schemas.DemoFileCreate(
                match_id=match_id,
                file_path=str(file_path),
                status=schemas.DemoFileStatus.DOWNLOADED,
            )
            created_demo = crud.create_demo_file(demo, conn)

            # Trigger automatic processing
            await self._parse_demo_async(created_demo.id)

            return created_demo

    async def request_demo_parsing(self, match_id: str) -> schemas.DemoFileRead:
        """Request parsing of a demo file.

        Args:
            match_id: The match ID to parse the demo for

        Returns:
            The updated demo file record

        Raises:
            HTTPException: If demo file is not found or not downloaded
        """
        with get_connection() as conn:
            # Get the demo file
            demo = crud.read_demo_file_by_match(match_id, conn)

            # Check if the demo file is downloaded
            if demo.status != schemas.DemoFileStatus.DOWNLOADED:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Demo file is not downloaded",
                )

            # Start parsing
            await self._parse_demo_async(demo.id)

            return demo

    async def get_demo_status(self, match_id: str) -> schemas.DemoFileRead:
        """Get the current status of a demo file.

        Args:
            match_id: Match ID of the demo file

        Returns:
            Demo file record

        Raises:
            HTTPException: If demo file is not found
        """
        with get_connection() as conn:
            return crud.read_demo_file_by_match(match_id, conn)

    async def list_demo_files(self) -> list[schemas.DemoFileRead]:
        """List all demo files.

        Returns:
            List of demo file records
        """
        with get_connection() as conn:
            return crud.list_demo_files(conn)

    async def _download_demo_async(self, match_id: str, demo_url: str) -> None:
        """Download demo file asynchronously.

        Args:
            match_id: Match ID for the demo
            demo_url: URL to download the demo from
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(demo_url)
                response.raise_for_status()
                data = response.content

                await self.demo_storage.save_demo_file(match_id, data)

                # Update status to downloaded
                with get_connection() as conn:
                    demo = crud.read_demo_file_by_match(match_id, conn)
                    crud.update_demo_file(
                        demo.id,
                        {"status": schemas.DemoFileStatus.DOWNLOADED.value},
                        conn,
                    )

        except (httpx.HTTPError, demo_storage.DemoStorageError) as e:
            logger.error(f"Failed to download demo {match_id}: {e}")
            # Update demo file status to failed
            with get_connection() as conn:
                demo = crud.read_demo_file_by_match(match_id, conn)
                crud.update_demo_file(
                    demo.id,
                    {
                        "status": schemas.DemoFileStatus.FAILED.value,
                        "error_message": str(e),
                    },
                    conn,
                )

    async def _parse_demo_async(self, demo_id: int) -> None:
        """Parse demo file asynchronously.

        Args:
            demo_id: ID of the demo file to parse
        """
        try:
            with get_connection() as conn:
                # Get the demo file
                demo = crud.read_demo_file(demo_id, conn)

                # Update status to processing
                crud.update_demo_file(
                    demo.id,
                    {"status": schemas.DemoFileStatus.PROCESSING.value},
                    conn,
                )

            # Request demo processing from microservice
            result = await self.demo_processing_client.request_demo_processing(
                demo_file_path=demo.file_path,
                timeout_seconds=300,
            )

            with get_connection() as conn:
                if result.success and result.demo_data:
                    # Create match record with the parsed data
                    match_data = self._convert_demo_data_to_match_data(result.demo_data)
                    crud.create_match(match_data, demo.id, conn)

                    # Update demo file status to processed
                    crud.update_demo_file(
                        demo.id,
                        {"status": schemas.DemoFileStatus.PROCESSED.value},
                        conn,
                    )
                else:
                    # Processing failed
                    crud.update_demo_file(
                        demo_id,
                        {
                            "status": schemas.DemoFileStatus.FAILED.value,
                            "error_message": result.error_message
                            or "Unknown processing error",
                        },
                        conn,
                    )

        except Exception as e:
            logger.error(f"Failed to parse demo {demo_id}: {e}")
            # Update demo file status to failed
            with get_connection() as conn:
                crud.update_demo_file(
                    demo_id,
                    {
                        "status": schemas.DemoFileStatus.FAILED.value,
                        "error_message": str(e),
                    },
                    conn,
                )

    def _convert_demo_data_to_match_data(
        self, demo_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Convert demo processing result to match data format.

        Args:
            demo_data: Raw demo data from processing service

        Returns:
            Match data in format expected by crud.create_match
        """
        map_info = demo_data.get("map_info", {})
        players_data = demo_data.get("players", [])
        rounds_data = demo_data.get("rounds", [])

        return {
            "map": {"name": map_info.get("name", "unknown")},
            "players": [
                {
                    "steam_id": player.get("steam_id", ""),
                    "name": player.get("name", ""),
                    "team": player.get("team", "UNKNOWN"),
                }
                for player in players_data
            ],
            "rounds": [
                {
                    "round_number": round_data.get("round_number", 0),
                    "winner": round_data.get("winner", "UNKNOWN"),
                    "win_reason": round_data.get("win_reason", "UNKNOWN"),
                    "ct_score": round_data.get("ct_score", 0),
                    "t_score": round_data.get("t_score", 0),
                }
                for round_data in rounds_data
            ],
            "match_info": demo_data.get("match_info", {}),
        }
