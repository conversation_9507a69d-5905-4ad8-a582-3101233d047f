"""Match management service."""

from dataclasses import dataclass

from backend import crud, schemas
from backend.clients.steam_api import MatchInfo, SteamAPI
from backend.database import get_connection
from backend.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class MatchService:
    """Service for managing matches and match data."""

    steam_api: SteamAPI

    @classmethod
    def create(cls, steam_api: SteamAPI) -> "MatchService":
        """Create a new MatchService instance.

        Args:
            steam_api: Steam API client

        Returns:
            MatchService instance
        """
        return cls(steam_api=steam_api)

    async def get_match_info(self, match_id: str) -> MatchInfo:
        """Get information about a match from Steam.

        Args:
            match_id: The match ID to get information for

        Returns:
            Match information from Steam API

        Raises:
            HTTPException: If match is not found
        """
        return await self.steam_api.get_match_info(match_id)

    def list_matches(self) -> list[schemas.MatchRead]:
        """List all matches.

        Returns:
            List of match records with players and rounds
        """
        with get_connection() as conn:
            return crud.list_matches(conn)

    def get_match(self, match_id: int) -> schemas.MatchRead:
        """Get a match by ID.

        Args:
            match_id: The ID of the match to retrieve

        Returns:
            Match record with players and rounds

        Raises:
            HTTPException: If match is not found
        """
        with get_connection() as conn:
            return crud.read_match(match_id, conn)

    def get_match_by_demo(self, demo_id: int) -> schemas.MatchRead:
        """Get match data for a demo file.

        Args:
            demo_id: The ID of the demo file

        Returns:
            Match record with players and rounds

        Raises:
            HTTPException: If match is not found
        """
        with get_connection() as conn:
            return crud.read_match_by_demo_file(demo_id, conn)
