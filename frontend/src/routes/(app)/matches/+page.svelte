<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import {
		Eye,
		Upload,
		Search,
		Calendar,
		Users,
		Target,
		Trophy,
		MapPin
	} from 'lucide-svelte';

	interface Match {
		id: number;
		map_name: string;
		created_at: string;
		demo_file_id: number | null;
		players: Array<{
			id: number;
			steam_id: string;
			name: string;
			team: string;
		}>;
		rounds: Array<{
			id: number;
			round_number: number;
			winner: string;
			win_reason: string;
			ct_score: number;
			t_score: number;
		}>;
	}

	interface PageData {
		matches: Match[];
	}

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	let searchQuery = $state('');
	let filteredMatches = $derived(
		data.matches.filter((match) => match.map_name.toLowerCase().includes(searchQuery.toLowerCase()))
	);

	function getMatchScore(match: Match) {
		if (match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return 'N/A';
		return `${lastRound.ct_score} - ${lastRound.t_score}`;
	}

	function getPlayerCount(match: Match) {
		return match.players.length;
	}

	function getMatchWinner(match: Match) {
		if (match.rounds.length === 0) return null;
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return null;
		return lastRound.ct_score > lastRound.t_score ? 'CT' : 'T';
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getMatchDuration(match: Match) {
		return match.rounds.length > 0 ? `${match.rounds.length} rounds` : 'N/A';
	}
</script>

<svelte:head>
	<title>Matches - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex flex-col gap-2">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">Matches</h1>
				<p class="text-muted-foreground">View and analyze your processed CS2 matches</p>
			</div>
			<Button href="/demos" class="gap-2">
				<Upload class="h-4 w-4" />
				Upload Demo
			</Button>
		</div>
	</div>

	<!-- Stats Overview -->
	{#if data.matches.length > 0}
		<div class="grid gap-4 md:grid-cols-4">
			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Total Matches</Card.Title>
					<Target class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{data.matches.length}</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Total Players</Card.Title>
					<Users class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">
						{data.matches.reduce((acc, match) => acc + match.players.length, 0)}
					</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Total Rounds</Card.Title>
					<Trophy class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">
						{data.matches.reduce((acc, match) => acc + match.rounds.length, 0)}
					</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Unique Maps</Card.Title>
					<MapPin class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">
						{new Set(data.matches.map((match) => match.map_name)).size}
					</div>
				</Card.Content>
			</Card.Root>
		</div>
	{/if}

	<!-- Search and Filters -->
	{#if data.matches.length > 0}
		<Card.Root>
			<Card.Header>
				<div class="flex items-center justify-between">
					<Card.Title>Search & Filter</Card.Title>
					<Badge variant="secondary">{filteredMatches.length} matches</Badge>
				</div>
			</Card.Header>
			<Card.Content>
				<div class="flex gap-4">
					<div class="relative flex-1">
						<Search
							class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground"
						/>
						<Input placeholder="Search by map name..." bind:value={searchQuery} class="pl-10" />
					</div>
				</div>
			</Card.Content>
		</Card.Root>
	{/if}

	<!-- Matches Table -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<Trophy class="h-5 w-5" />
				{filteredMatches.length > 0 ? 'Match Results' : 'All Matches'}
			</Card.Title>
			<Card.Description>
				{#if searchQuery}
					Showing {filteredMatches.length} matches matching "{searchQuery}"
				{:else}
					Matches processed from uploaded demo files
				{/if}
			</Card.Description>
		</Card.Header>
		<Card.Content>
			{#if filteredMatches.length > 0}
				<div class="rounded-md border">
					<Table.Root>
						<Table.Header>
							<Table.Row>
								<Table.Head class="w-[150px]">Map</Table.Head>
								<Table.Head class="w-[100px]">Score</Table.Head>
								<Table.Head class="w-[80px]">Winner</Table.Head>
								<Table.Head class="w-[80px]">Players</Table.Head>
								<Table.Head class="w-[100px]">Duration</Table.Head>
								<Table.Head class="w-[120px]">Processed</Table.Head>
								<Table.Head class="w-[100px]">Actions</Table.Head>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{#each filteredMatches as match}
								<Table.Row class="hover:bg-muted/50">
									<Table.Cell>
										<div class="flex items-center gap-2">
											<div
												class="flex h-8 w-8 items-center justify-center rounded bg-gradient-to-br from-orange-400 to-red-500 text-xs font-bold text-white"
											>
												{match.map_name.slice(0, 2).toUpperCase()}
											</div>
											<div>
												<div class="font-medium">{match.map_name}</div>
											</div>
										</div>
									</Table.Cell>
									<Table.Cell>
										<Badge variant="outline" class="font-mono">
											{getMatchScore(match)}
										</Badge>
									</Table.Cell>
									<Table.Cell>
										{#if getMatchWinner(match)}
											<Badge
												variant={getMatchWinner(match) === 'CT' ? 'default' : 'secondary'}
												class="text-xs"
											>
												{getMatchWinner(match)}
											</Badge>
										{:else}
											<span class="text-sm text-muted-foreground">N/A</span>
										{/if}
									</Table.Cell>
									<Table.Cell>
										<div class="flex items-center gap-1">
											<Users class="h-3 w-3 text-muted-foreground" />
											<span class="text-sm">{getPlayerCount(match)}</span>
										</div>
									</Table.Cell>
									<Table.Cell>
										<span class="text-sm text-muted-foreground">
											{getMatchDuration(match)}
										</span>
									</Table.Cell>
									<Table.Cell>
										<div class="flex items-center gap-1 text-sm text-muted-foreground">
											<Calendar class="h-3 w-3" />
											{formatDate(match.created_at)}
										</div>
									</Table.Cell>
									<Table.Cell>
										<Button href="/matches/{match.id}" size="sm" variant="outline" class="gap-2">
											<Eye class="h-3 w-3" />
											View
										</Button>
									</Table.Cell>
								</Table.Row>
							{/each}
						</Table.Body>
					</Table.Root>
				</div>
			{:else if searchQuery}
				<!-- No search results -->
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<Search class="mb-4 h-12 w-12 text-muted-foreground/30" />
					<h3 class="mb-2 font-medium">No matches found</h3>
					<p class="mb-4 text-sm text-muted-foreground">
						No matches found matching "{searchQuery}". Try a different search term.
					</p>
					<Button variant="outline" onclick={() => (searchQuery = '')}>Clear Search</Button>
				</div>
			{:else}
				<!-- No matches at all -->
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<div
						class="mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-gradient-to-br from-orange-400 to-red-500"
					>
						<Trophy class="h-8 w-8 text-white" />
					</div>
					<h3 class="mb-2 font-medium">No matches processed yet</h3>
					<p class="mb-6 max-w-sm text-sm text-muted-foreground">
						Upload your first CS2 demo file to start analyzing your matches and see detailed
						statistics.
					</p>
					<div class="flex gap-3">
						<Button href="/demos" class="gap-2">
							<Upload class="h-4 w-4" />
							Upload Your First Demo
						</Button>
					</div>
				</div>
			{/if}
		</Card.Content>
	</Card.Root>
</div>
